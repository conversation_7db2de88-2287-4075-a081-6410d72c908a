#!/bin/bash

# 简化的OpenQuant启动脚本（用于远程服务器）
# 这个脚本会被部署到远程服务器上使用

set -e

# 配置
OPENQUANT_BINARY="./open_quant"
CONFIG_FILE="config.toml"
PID_FILE="openquant.pid"
LOG_FILE="openquant.log"
MAX_RESTART_COUNT=5
RESTART_DELAY=3

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

# 检查是否已经在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # 正在运行
        else
            rm -f "$PID_FILE"
            return 1  # 不在运行
        fi
    fi
    return 1  # 不在运行
}

# 停止OpenQuant
stop_openquant() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log "Stopping OpenQuant (PID: $pid)..."
            kill -TERM "$pid" 2>/dev/null || true

            # 等待进程停止
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done

            # 如果还在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                log "Force killing OpenQuant..."
                kill -KILL "$pid" 2>/dev/null || true
            fi

            rm -f "$PID_FILE"
            log_success "OpenQuant stopped"
        else
            rm -f "$PID_FILE"
        fi
    else
        log "OpenQuant is not running"
    fi
}

# 启动OpenQuant
start_openquant() {
    if check_running; then
        log "OpenQuant is already running"
        return 0
    fi

    # 检查文件
    if [ ! -f "$OPENQUANT_BINARY" ]; then
        log_error "OpenQuant binary not found: $OPENQUANT_BINARY"
        return 1
    fi

    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "Config file not found: $CONFIG_FILE"
        return 1
    fi

    # 使二进制文件可执行
    chmod +x "$OPENQUANT_BINARY"

    log "Starting OpenQuant..."

    # 启动进程
    nohup "$OPENQUANT_BINARY" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"

    # 检查是否启动成功
    sleep 2
    if kill -0 "$pid" 2>/dev/null; then
        log_success "OpenQuant started successfully (PID: $pid)"
        return 0
    else
        log_error "OpenQuant failed to start"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 重启OpenQuant
restart_openquant() {
    log "Restarting OpenQuant..."
    stop_openquant
    sleep 2
    start_openquant
}

# 显示状态
status_openquant() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        log_success "OpenQuant is running (PID: $pid)"

        # 显示最后几行日志
        if [ -f "$LOG_FILE" ]; then
            echo ""
            echo "Last 5 lines of log:"
            tail -n 5 "$LOG_FILE"
        fi
    else
        log "OpenQuant is not running"

        # 如果有日志文件，显示最后几行
        if [ -f "$LOG_FILE" ]; then
            echo ""
            echo "Last 5 lines of log:"
            tail -n 5 "$LOG_FILE"
        fi
    fi
}

# 自动重启模式
auto_restart() {
    local restart_count=0

    log "Starting auto-restart mode..."

    # 清理函数
    cleanup() {
        log "Received signal, stopping auto-restart..."
        stop_openquant
        exit 0
    }

    trap cleanup SIGINT SIGTERM

    while true; do
        if [ $restart_count -ge $MAX_RESTART_COUNT ]; then
            log_error "Maximum restart count reached. Exiting."
            exit 1
        fi

        if ! check_running; then
            if [ $restart_count -gt 0 ]; then
                log "Waiting $RESTART_DELAY seconds before restart..."
                sleep $RESTART_DELAY
            fi

            log "Starting OpenQuant (attempt $((restart_count + 1)))..."
            if start_openquant; then
                restart_count=0  # 重置计数器
            else
                restart_count=$((restart_count + 1))
                log_error "Start failed, will retry..."
            fi
        fi

        sleep 5  # 检查间隔
    done
}

# 显示帮助
show_help() {
    echo "Usage: $0 {start|stop|restart|status|auto|help}"
    echo ""
    echo "Commands:"
    echo "  start      Start OpenQuant"
    echo "  stop       Stop OpenQuant"
    echo "  restart    Restart OpenQuant"
    echo "  status     Show OpenQuant status"
    echo "  auto       Start auto-restart mode"
    echo "  help       Show this help message"
}

# 主逻辑
case "${1:-help}" in
    start)
        start_openquant
        ;;
    stop)
        stop_openquant
        ;;
    restart)
        restart_openquant
        ;;
    status)
        status_openquant
        ;;
    auto)
        auto_restart
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
