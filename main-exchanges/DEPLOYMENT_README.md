# OpenQuant 部署和管理脚本

这个目录包含了用于自动部署和管理OpenQuant进程的脚本。

## 脚本说明

### 1. `deploy.sh` - 自动部署脚本

自动将配置文件和策略文件部署到各个交易所的远程服务器。

#### 功能特性
- 支持批量部署到多个交易所
- 自动重启远程OpenQuant进程
- 支持干运行模式（预览部署操作）
- 详细的日志输出和错误处理
- 自动检查SSH连接和文件完整性

#### 使用方法

```bash
# 部署到所有交易所
./deploy.sh

# 部署到所有交易所（显式指定）
./deploy.sh all

# 部署到特定交易所
./deploy.sh okx
./deploy.sh bitget
./deploy.sh bybit

# 部署到多个交易所
./deploy.sh okx,bitget

# 干运行模式（预览操作，不实际执行）
./deploy.sh --dry-run okx

# 强制部署（即使文件相同）
./deploy.sh --force okx

# 显示帮助信息
./deploy.sh --help
```

#### 配置

脚本中的交易所主机映射：
```bash
EXCHANGE_HOSTS["okx"]="okx-trend"
EXCHANGE_HOSTS["bitget"]="bitget-trend"
EXCHANGE_HOSTS["bybit"]="bybit-trend"
```

部署的文件：
- `config.toml` - 交易所配置文件
- `strategy.py` - 策略文件
- `start_openquant.sh` - OpenQuant启动脚本

### 2. `start_openquant.sh` - OpenQuant管理脚本

用于在远程服务器上管理OpenQuant进程的脚本。

#### 功能特性
- 启动/停止/重启OpenQuant进程
- 进程状态检查
- 自动重启模式（进程崩溃时自动重启）
- PID文件管理
- 日志文件管理

#### 使用方法

```bash
# 启动OpenQuant
./start_openquant.sh start

# 停止OpenQuant
./start_openquant.sh stop

# 重启OpenQuant
./start_openquant.sh restart

# 查看状态
./start_openquant.sh status

# 自动重启模式（推荐用于生产环境）
./start_openquant.sh auto

# 显示帮助
./start_openquant.sh help
```

#### 自动重启模式

自动重启模式会持续监控OpenQuant进程，如果进程崩溃会自动重启：

```bash
# 在后台运行自动重启模式
nohup ./start_openquant.sh auto > auto_restart.log 2>&1 &

# 停止自动重启模式
pkill -f "start_openquant.sh auto"
```

### 3. `run_openquant.sh` - 本地开发用自动重启脚本

用于本地开发和测试的OpenQuant自动重启脚本。

#### 使用方法

```bash
# 启动自动重启守护进程
./run_openquant.sh [exchange_name]

# 例如
./run_openquant.sh okx
```

### 4. `check_status.sh` - 状态检查和管理脚本

用于检查和管理所有交易所OpenQuant运行状态的脚本。

#### 使用方法

```bash
# 检查所有交易所状态
./check_status.sh status

# 检查特定交易所状态
./check_status.sh status okx

# 停止所有交易所
./check_status.sh stop

# 停止特定交易所
./check_status.sh stop okx

# 启动所有交易所自动重启模式
./check_status.sh start

# 启动特定交易所自动重启模式
./check_status.sh start okx

# 重启所有交易所
./check_status.sh restart

# 重启特定交易所
./check_status.sh restart okx
```

## 部署流程

### 一键部署（推荐）

**现在部署脚本会自动启动自动重启模式，实现真正的一键部署！**

1. 确保SSH密钥配置正确，可以无密码登录到远程服务器
2. 检查交易所主机名配置是否正确
3. 运行部署脚本：

```bash
# 先进行干运行检查
./deploy.sh --dry-run all

# 一键部署到所有交易所（自动启动自动重启模式）
./deploy.sh all
```

部署完成后，OpenQuant会自动运行，并且如果进程退出会自动重启！

### 日常更新

当你修改了策略文件或配置文件后：

```bash
# 部署到特定交易所（会自动重启）
./deploy.sh okx

# 或部署到所有交易所
./deploy.sh all
```

### 状态检查

```bash
# 检查所有交易所运行状态
./check_status.sh status

# 检查特定交易所
./check_status.sh status okx
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查SSH密钥配置
   - 确认主机名解析正确
   - 测试手动SSH连接

2. **文件传输失败**
   - 检查本地文件是否存在
   - 确认远程目录权限
   - 检查磁盘空间

3. **OpenQuant启动失败**
   - 检查二进制文件权限
   - 查看日志文件内容
   - 确认配置文件格式正确

### 日志查看

```bash
# 查看部署日志（本地）
./deploy.sh --dry-run okx

# 查看OpenQuant日志（远程）
ssh okx-trend "tail -f openquant.log"

# 查看自动重启日志（远程）
ssh okx-trend "tail -f auto_restart.log"
```

### 手动操作

如果自动脚本出现问题，可以手动操作：

```bash
# 手动复制文件
scp config.toml okx-trend:~/
scp strategy.py okx-trend:~/

# 手动重启OpenQuant
ssh okx-trend "pkill -f openquant && ./openquant &"
```

## 安全注意事项

1. 确保SSH密钥安全存储
2. 定期更新远程服务器
3. 监控日志文件大小，定期清理
4. 备份重要配置文件

## 扩展

要添加新的交易所：

1. 在 `deploy.sh` 中添加主机映射
2. 创建对应的配置目录和文件
3. 更新 `DEFAULT_EXCHANGES` 数组

```bash
# 在deploy.sh中添加
EXCHANGE_HOSTS["new_exchange"]="new-exchange-host"
DEFAULT_EXCHANGES=("okx" "bitget" "bybit" "new_exchange")
```
